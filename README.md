# Browser Scripts - TypeScript Monorepo

这是一个用于开发油猴脚本的 TypeScript monorepo 项目。

## 项目结构

```
browser-scripts/
├── packages/                 # 各个脚本包
│   └── vrchat-link/         # VRChat 链接复制脚本
├── shared/                  # 共享代码
│   ├── types/              # 类型定义
│   └── utils/              # 工具函数
├── tools/                  # 构建工具（可选）
├── package.json            # 根配置
├── pnpm-workspace.yaml     # pnpm 工作区配置
├── tsconfig.json           # TypeScript 根配置
├── .eslintrc.js           # ESLint 配置
├── .prettierrc            # Prettier 配置
└── README.md              # 项目说明
```

## 快速开始

### 安装依赖

```bash
pnpm install
```

### 构建所有脚本

```bash
pnpm build
```

### 开发模式（监听文件变化）

```bash
pnpm dev
```

### 代码检查和格式化

```bash
# 代码检查
pnpm lint

# 代码格式化
pnpm format

# 类型检查
pnpm type-check
```

## 创建新脚本

1. 在 `packages/` 目录下创建新的脚本目录
2. 复制现有脚本的配置文件（`package.json`, `tsconfig.json`, `webpack.config.js`）
3. 修改配置文件中的脚本信息
4. 在 `src/index.ts` 中编写脚本代码
5. 更新根目录的 `tsconfig.json` 中的 `references` 配置

## 共享代码

### 类型定义 (`shared/types`)

包含所有共享的 TypeScript 类型定义：
- `userscript.ts` - 油猴脚本相关类型
- `dom.ts` - DOM 操作相关类型
- `common.ts` - 通用类型定义

### 工具函数 (`shared/utils`)

包含常用的工具函数：
- `dom.ts` - DOM 操作工具
- `storage.ts` - 存储管理工具
- `logger.ts` - 日志工具
- `request.ts` - HTTP 请求工具
- `userscript.ts` - 油猴脚本工具

## 使用示例

```typescript
import { createElement, setStyles, logger } from '@shared/utils';
import type { CreateElementConfig } from '@shared/types';

// 创建元素
const button = createElement({
  tag: 'button',
  textContent: '点击我',
  styles: {
    background: '#007bff',
    color: 'white',
    padding: '10px 20px',
    border: 'none',
    borderRadius: '4px'
  }
});

// 记录日志
logger.info('按钮已创建');
```

## 脚本列表

### VRChat 链接复制脚本

- **路径**: `packages/vrchat-link`
- **功能**: 在 B 站视频和直播页面添加 VRChat 链接复制功能
- **匹配**: `https://www.bilibili.com/video/BV*`, `https://live.bilibili.com/*`

## 开发指南

### 代码规范

- 使用 TypeScript 编写代码
- 遵循 ESLint 和 Prettier 配置
- 为公共函数添加 JSDoc 注释
- 使用有意义的变量和函数名

### 构建配置

每个脚本包都使用 Webpack 进行构建，配置包括：
- TypeScript 编译
- 代码打包
- 油猴脚本头部生成
- 开发模式下的监听

### 类型安全

项目使用严格的 TypeScript 配置，确保类型安全：
- 启用 `strict` 模式
- 使用 `@types/tampermonkey` 和 `@types/greasemonkey` 提供油猴 API 类型
- 共享类型定义确保一致性

## 许可证

ISC
