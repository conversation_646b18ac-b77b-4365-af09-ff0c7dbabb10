{"name": "browser-scripts", "version": "1.0.0", "description": "TypeScript monorepo for developing userscripts", "private": true, "scripts": {"build": "pnpm build:shared && pnpm build:packages", "build:shared": "pnpm -r --filter \"./shared/*\" run build", "build:packages": "pnpm -r --filter \"./packages/*\" run build", "build:tools": "pnpm -r --filter \"./tools/*\" run build", "dev": "ts-node scripts/dev.ts", "dev:shared": "pnpm -r --filter \"./shared/*\" run dev", "lint": "pnpm -r --filter \"./packages/*\" run lint", "lint:fix": "pnpm -r --filter \"./packages/*\" run lint -- --fix", "type-check": "pnpm -r run type-check", "format": "prettier --write \"packages/**/*.{ts,js,json}\" \"shared/**/*.{ts,js,json}\" \"tools/**/*.{ts,js,json}\" \"scripts/**/*.{ts,js,json}\" \"*.{js,json,yaml,md}\"", "format:check": "prettier --check \"packages/**/*.{ts,js,json}\" \"shared/**/*.{ts,js,json}\" \"tools/**/*.{ts,js,json}\" \"scripts/**/*.{ts,js,json}\" \"*.{js,json,yaml,md}\"", "clean": "pnpm -r run clean && rm -rf node_modules", "clean:dist": "find . -name 'dist' -type d -exec rm -rf {} + 2>/dev/null || true", "postinstall": "pnpm build:tools && pnpm build:shared"}, "keywords": ["userscript", "tampermonkey", "greasemonkey", "typescript", "monorepo"], "author": "", "license": "ISC", "packageManager": "pnpm@10.6.2", "devDependencies": {"@types/node": "^22.15.21", "@types/tampermonkey": "^5.0.3", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "esbuild": "^0.25.5", "esbuild-plugin-userscript": "^0.2.6", "eslint": "^9.0.0", "prettier": "^3.0.0", "ts-loader": "^9.5.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}