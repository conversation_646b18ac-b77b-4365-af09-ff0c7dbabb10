import path from 'path';
import { watchUserscript, ensureDir } from '../../tools/build/esbuild.config';
import { metadata } from './userscript.config';
import packageJson from './package.json';

async function main() {
  const outfile = path.resolve(__dirname, 'dist', packageJson.name + '.user.js');
  
  await ensureDir(outfile);
  
  await watchUserscript({
    entryPoint: path.resolve(__dirname, 'src', 'index.ts'),
    outfile,
    metadata,
    development: true
  });
}

main().catch(error => {
  console.error('❌ 开发模式启动失败:', error);
  process.exit(1);
});