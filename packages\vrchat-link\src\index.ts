import './index.css';

let prefixes = [
  { label: '直接复制当前连接', isDirectLink: true, prefix: '' },
  { label: '纯K', prefix: 'http://ckapi.sevenbrothers.cn/bili/api?id=' },
  { label: 'api xin', prefix: 'http://api.xin.moe/' },
  { label: '备用链接', prefix: 'http://anotherprefix.com/link?id=' },
];

function fetchPrefixes() {
  fetch('https://api.aryz.site/configs/vrchat-copy-url', {
    credentials: 'include',
    cache: 'no-store',
  })
    .then(response => response.json())
    .then(data => {
      prefixes = data;
      updateMenu();
    })
    .catch(error => console.error('获取链接前缀失败:', error));
}

// 从URL中获取查询参数
function getQueryString(name: string) {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  let r = window.location.search.substr(1).match(reg);
  if (r != null) {
    return decodeURIComponent(r[2]);
  }
  return null;
}

// 从路径中获取BV号或房间号（支持视频和直播）
function getUrlParameter() {
  const re = /^.*(BV[0-9a-zA-Z]+).*$/;
  const re2 = /^\/([0-9]+)\?*.*$/;
  const uri = re.exec(window.location.pathname);
  const uri2 = re2.exec(window.location.pathname);
  let parameter = '';
  if (uri && uri.length === 2) {
    parameter = uri[1];
    let page = getQueryString('p');
    if (page) parameter = parameter + '/' + page;
  } else if (uri2 && uri2.length === 2) {
    parameter = uri2[1];
  }
  return parameter;
}

// 创建一个容器，用于包含按钮和下拉菜单
const containerDiv = document.createElement('div');
containerDiv.style.position = 'fixed';
containerDiv.style.bottom = '1vh';
containerDiv.style.left = '0.5vw';
containerDiv.style.zIndex = '999';
containerDiv.style.fontSize = '16px';
containerDiv.style.lineHeight = '16px';
containerDiv.style.fontFamily = 'sans-serif';

// 创建主按钮
const mainButton = document.createElement('div');
mainButton.textContent = '复制链接给VRChat使用';
mainButton.style.boxSizing = 'border-box';
mainButton.style.display = 'flex';
mainButton.style.justifyContent = 'center';
mainButton.style.alignItems = 'center';
mainButton.style.whiteSpace = 'nowrap';
mainButton.style.overflow = 'hidden';
mainButton.style.textOverflow = 'ellipsis';
mainButton.style.background = '#FB7299';
mainButton.style.color = '#FFF';
mainButton.style.padding = '6px 8px';
mainButton.style.borderRadius = '8px';
mainButton.style.cursor = 'pointer';
mainButton.style.transition = 'all 0.3s ease-in';

// 根据文本内容计算宽度并更新按钮
function updateMainButtonText(newText: string) {
  const numChinese = newText.match(/[\u3400-\u9FBF]/g)
    ? (newText.match(/[\u3400-\u9FBF]/g)?.length ?? 0)
    : 0;
  const numEnglish = newText.length - numChinese;
  const padding = 16;
  const width = numChinese * 16 + numEnglish * 10 + padding + 'px';
  mainButton.style.width = width;
  mainButton.textContent = newText;
}
updateMainButtonText('复制链接给VRChat使用');

// 创建下拉菜单容器（默认隐藏）
const menuDiv = document.createElement('div');
menuDiv.style.position = 'absolute';
menuDiv.style.bottom = '100%';
menuDiv.style.left = '0';
menuDiv.style.marginBottom = '4px';
menuDiv.style.background = '#FFF';
menuDiv.style.border = '1px solid #ccc';
menuDiv.style.borderRadius = '4px';
menuDiv.style.boxShadow = '0 2px 6px rgba(0,0,0,0.2)';
menuDiv.style.display = 'none';
menuDiv.style.flexDirection = 'column';
menuDiv.style.minWidth = '200px';
menuDiv.style.fontSize = '14px';
menuDiv.style.color = '#333';
menuDiv.style.overflow = 'hidden';

function updateMenu() {
  menuDiv.innerHTML = '';
  prefixes.forEach(option => {
    const item = document.createElement('div');
    item.textContent = option.label;
    item.style.padding = '8px 12px';
    item.style.cursor = 'pointer';
    item.style.borderBottom = '1px solid #eee';
    item.addEventListener('mouseover', () => {
      item.style.background = '#f5f5f5';
    });
    item.addEventListener('mouseout', () => {
      item.style.background = '#FFF';
    });
    item.addEventListener('click', () => {
      const link = option.isDirectLink
        ? window.location.origin + window.location.pathname
        : option.prefix + getUrlParameter();
      navigator.clipboard
        .writeText(link)
        .then(() => {
          updateMainButtonText('复制成功');
        })
        .catch(() => {
          updateMainButtonText('复制失败');
        });
      setTimeout(() => {
        menuDiv.style.display = 'none';
      }, 300);
      setTimeout(() => {
        updateMainButtonText('复制链接给VRChat使用');
      }, 5000);
    });
    menuDiv.appendChild(item);
  });
}

// 将主按钮和菜单添加到容器中
containerDiv.appendChild(mainButton);
containerDiv.appendChild(menuDiv);
document.body.appendChild(containerDiv);

// fetchPrefixes();
updateMenu();

let hideMenuTimer: Timeout | null = null;

containerDiv.addEventListener('mouseenter', () => {
  if (hideMenuTimer) {
    clearTimeout(hideMenuTimer);
    hideMenuTimer = null;
  }
  menuDiv.style.display = 'flex';
});

containerDiv.addEventListener('mouseleave', () => {
  hideMenuTimer = setTimeout(() => {
    menuDiv.style.display = 'none';
    hideMenuTimer = null;
  }, 300); // 延迟300毫秒关闭菜单
});
