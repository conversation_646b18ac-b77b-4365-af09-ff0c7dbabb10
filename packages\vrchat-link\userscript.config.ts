import type { UserscriptMetadata } from '../../tools/build-tools/esbuild.config';
import packageJson from './package.json';

export const metadata: UserscriptMetadata = {
  name: 'B站VRChat链接复制（下拉选项版）',
  namespace: 'http://tampermonkey.net/',
  version: packageJson.version,
  description: packageJson.description,
  author: '<PERSON><PERSON>z',
  match: ['https://www.bilibili.com/video/BV*', 'https://live.bilibili.com/*'],
  grant: ['none'],
  downloadURL:
    'https://update.greasyfork.org/scripts/485486/B%E7%AB%99VRChat%E9%93%BE%E6%8E%A5%E5%A4%8D%E5%88%B6.user.js',
  updateURL:
    'https://update.greasyfork.org/scripts/485486/B%E7%AB%99VRChat%E9%93%BE%E6%8E%A5%E5%A4%8D%E5%88%B6.meta.js',
  'run-at': 'document-end',
};
